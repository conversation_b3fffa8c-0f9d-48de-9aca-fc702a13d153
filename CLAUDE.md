# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive JavaScript toolkit for blockchain interactions, supporting EVM chains, TON blockchain transactions, and RPC testing infrastructure. The project contains specialized scripts for transaction signing, BOC generation, EIP-7702 testing, and Arbitrum RPC performance testing.

## Dependencies

- `ethers@^6.15.0` - EVM chain interactions (used in sign_tx.js, eip7702_test.js)
- `@ton/ton@^15.3.1` - TON blockchain core library (used in ton_transfer_boc.js)
- `@ton/crypto@^3.3.0` - TON cryptographic functions (used in ton_transfer_boc.js)
- `@ton/core@^0.61.0` - TON core data structures (used in ton_transfer_boc.js)
- `dotenv@^16.6.1` - Environment variable management (used in sign_tx.js)

## Common Commands

### Install Dependencies
```bash
npm install
```

### Blockchain Transaction Scripts
```bash
# EVM transaction signing (uses environment variables)
node sign_tx.js
node sign_tx.js --send  # Send transaction to network
node sign_tx.js --rpc https://polygon-rpc.com --send

# TON transfer BOC generation
node ton_transfer_boc.js
node ton_transfer_boc.js --send
node ton_transfer_boc.js --endpoint https://custom-ton-api.com --send

# EIP-7702 testing (Polygon mainnet)
node eip7702_test.js --private-key YOUR_KEY --rpc https://polygon-rpc.com
node eip7702_test.js --private-key YOUR_KEY --sponsor-key SPONSOR_KEY --rpc https://polygon-rpc.com
```

### Arbitrum RPC Testing (requires K6)
```bash
# Install K6 first: brew install k6 (macOS) or sudo apt install k6 (Ubuntu)

# Quick issue reproduction
./run_arbitrum_tests.sh quick

# Full performance testing
./run_arbitrum_tests.sh full

# Advanced multi-scenario testing
./run_arbitrum_tests.sh advanced

# Direct K6 execution
k6 run arbitrum_issue_reproduce.js
k6 run arbitrum_blockpi_test.js
k6 run arbitrum_advanced_test.js
```

## Architecture

### EVM Chain Scripts
- **sign_tx.js**: Universal EVM transaction signer with comprehensive gas estimation
  - Uses ethers.js v6 with EIP-1559 support
  - Configuration via environment variables (.env file)
  - Automatic gas limit estimation with 20% safety buffer
  - Supports both signature-only and send-to-network modes
  - Built-in network detection and balance validation

- **eip7702_test.js**: Complete EIP-7702 (Set Code Transaction) testing toolkit
  - Supports authorization list construction
  - Non-sponsored and sponsored transaction modes
  - Delegation status checking and revocation
  - Targets Polygon mainnet by default
  - Comprehensive error handling for EIP-7702 specific issues

### TON Blockchain Scripts
- **ton_transfer_boc.js**: TON transfer BOC (Bag of Cells) generator
  - Multi-wallet version support (W5, V4, V3)
  - Automatic wallet version detection from mnemonic
  - Balance validation and fee estimation
  - BOC generation in both base64 and hex formats
  - Optional transaction sending capability

### Arbitrum RPC Testing Infrastructure
- **run_arbitrum_tests.sh**: Main test launcher with multiple test scenarios
- **arbitrum_issue_reproduce.js**: Simple issue reproduction script
- **arbitrum_blockpi_test.js**: Complete performance testing with detailed metrics
- **arbitrum_advanced_test.js**: Multi-scenario testing with load variations
- **validate_scripts.js**: Script validation utility

## Configuration Patterns

### Environment Variables (EVM scripts)
```bash
PRIVATE_KEY=your_private_key_here
RECIPIENT_ADDRESS=0x_recipient_address
DEFAULT_RPC_URL=https://your-rpc-endpoint
```

### Hard-coded Configuration (TON scripts)
- Configuration objects embedded within scripts
- Mnemonic phrases and recipient addresses coded directly
- Fallback API endpoints for reliability

### K6 Testing Configuration
- Test parameters configurable via command line arguments
- Virtual user counts and duration settings
- Custom RPC endpoint configuration
- Output format options (JSON, console)

## Key Implementation Details

### Gas Estimation Strategy
- EVM scripts use `provider.estimateGas()` with 20% buffer
- Fallback to 21000 for simple transfers if estimation fails
- Automatic EIP-1559 fee data retrieval

### Error Handling Patterns
- Standardized error handling with specific error code detection
- Network-specific error messages (INSUFFICIENT_FUNDS, NONCE_EXPIRED, etc.)
- Contextual troubleshooting suggestions for common issues

### RPC Testing Methodology
- Sequential testing: `eth_blockNumber` → `eth_getBlockByNumber` → `eth_getBlockByHash`
- Data consistency validation between different retrieval methods
- Performance benchmarking with response time thresholds
- Load testing with configurable virtual users

### Security Considerations
- Private key validation (64-character hex strings)
- Address format validation
- Balance validation before transaction submission
- No sensitive data logging
- Testnet recommendations for initial testing

## Script Interactions

The scripts operate independently but share common patterns:
- Command-line argument parsing with similar flag structures
- Progressive output logging for debugging
- Network connection validation
- Transaction receipt monitoring and confirmation waiting
- Comprehensive help systems (`--help` or `-h`)

## Important Notes

- EIP-7702 support is network-dependent and may not be activated on all chains
- TON scripts use mainnet by default - modify endpoints for testnet usage
- Arbitrum testing requires external K6 installation
- No test framework is currently implemented in this project
- All blockchain scripts include safety checks and balance validation