#!/bin/bash

# K6 Arbitrum RPC 测试运行脚本
# 使用方法: ./run_k6_arbitrum_test.sh [OPTIONS]

# 默认参数
DEFAULT_VUS=1
DEFAULT_RPS=1
DEFAULT_DURATION="30s"
DEFAULT_API_KEY="your_api_key_here"

# 显示帮助信息
show_help() {
    cat << EOF
K6 Arbitrum RPC 测试脚本

使用方法: $0 [OPTIONS]

选项:
    --vus VUS               虚拟用户数 (默认: $DEFAULT_VUS)
    --rps RPS               每秒请求数 (默认: $DEFAULT_RPS)
    --duration DURATION     测试持续时间 (默认: $DEFAULT_DURATION)
    --api-key API_KEY       BlockPI API Key (默认: $DEFAULT_API_KEY)
    --help                  显示此帮助信息

示例:
    # 使用默认参数运行
    $0

    # 低频测试 - 1个用户，每秒1个请求，持续1分钟
    $0 --vus 1 --rps 1 --duration 60s --api-key your_actual_api_key

    # 中等负载测试 - 5个用户，每秒5个请求，持续2分钟
    $0 --vus 5 --rps 5 --duration 2m --api-key your_actual_api_key

    # 高负载测试 - 10个用户，每秒10个请求，持续5分钟
    $0 --vus 10 --rps 10 --duration 5m --api-key your_actual_api_key

注意:
    - 请确保已安装 k6: https://k6.io/docs/getting-started/installation/
    - 请替换 your_actual_api_key 为你的真实 BlockPI API Key
    - 时间单位支持: s(秒), m(分钟), h(小时)
EOF
}

# 解析命令行参数
VUS=$DEFAULT_VUS
RPS=$DEFAULT_RPS
DURATION=$DEFAULT_DURATION
API_KEY=$DEFAULT_API_KEY

while [[ $# -gt 0 ]]; do
    case $1 in
        --vus)
            VUS="$2"
            shift 2
            ;;
        --rps)
            RPS="$2"
            shift 2
            ;;
        --duration)
            DURATION="$2"
            shift 2
            ;;
        --api-key)
            API_KEY="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 验证参数
if ! [[ "$VUS" =~ ^[0-9]+$ ]] || [ "$VUS" -lt 1 ]; then
    echo "错误: VUS 必须是大于0的整数"
    exit 1
fi

if ! [[ "$RPS" =~ ^[0-9]+$ ]] || [ "$RPS" -lt 1 ]; then
    echo "错误: RPS 必须是大于0的整数"
    exit 1
fi

# 检查 k6 是否已安装
if ! command -v k6 &> /dev/null; then
    echo "错误: k6 未安装"
    echo "请访问 https://k6.io/docs/getting-started/installation/ 安装 k6"
    exit 1
fi

# 检查测试脚本是否存在
if [ ! -f "k6_arbitrum_rpc_test.js" ]; then
    echo "错误: 找不到测试脚本 k6_arbitrum_rpc_test.js"
    exit 1
fi

# 显示测试配置
echo "========================================"
echo "K6 Arbitrum RPC 测试配置"
echo "========================================"
echo "虚拟用户数 (VUS): $VUS"
echo "每秒请求数 (RPS): $RPS"
echo "测试持续时间: $DURATION"
echo "API Key: ${API_KEY:0:10}..." # 只显示前10个字符
echo "========================================"
echo ""

# 运行 k6 测试
echo "开始运行 k6 测试..."
echo ""

k6 run \
    --env VUS="$VUS" \
    --env RPS="$RPS" \
    --env DURATION="$DURATION" \
    --env API_KEY="$API_KEY" \
    k6_arbitrum_rpc_test.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "测试完成！"
    echo "========================================"
else
    echo ""
    echo "========================================"
    echo "测试失败！请检查错误信息。"
    echo "========================================"
    exit 1
fi
