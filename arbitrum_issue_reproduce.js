import { check } from "k6";
import http from "k6/http";

/**
 * 简化版 K6 脚本 - 专门用于复现 BlockPI Arbitrum RPC 问题
 * 测试流程: eth_blockNumber -> eth_getBlockByNumber -> eth_getTransactionReceipt
 */

export let options = {
  vus: 1, // 单用户测试，便于观察问题
  // iterations: 5, // 执行5次迭代
  duration: "10m", // 最多运行30秒
};

// 错误记录数组
let errorMessages = [];

// 简单的错误记录函数
function recordError(message) {
  const timestamp = new Date().toISOString();
  const errorLine = `[${timestamp}] 迭代${__ITER + 1}: ${message}`;
  errorMessages.push(errorLine);
}

// BlockPI Arbitrum RPC 端点
const RPC_URL =
  "https://arbitrum.blockpi.network/v1/rpc/dcc313908317c15418d3efd92e856964fb894146";

// 请求配置
const requestConfig = {
  headers: { "Content-Type": "application/json" },
  timeout: "10s",
};

export default function () {
  console.log(`\n🔄 开始第 ${__ITER + 1} 次测试...`);

  // 步骤1: 获取最新区块号
  const blockNumber = getBlockNumber();
  if (!blockNumber) return;

  // 步骤2: 通过区块号获取区块信息
  const blockInfo = getBlockByNumber(blockNumber);
  if (!blockInfo) return;

  // 步骤3: 获取区块中第一笔交易的receipt
  getTransactionReceipt(blockInfo.firstTransactionHash);

  console.log(`✅ 第 ${__ITER + 1} 次测试完成\n`);
}

/**
 * 获取最新区块号
 */
function getBlockNumber() {
  console.log("📡 1. 调用 eth_blockNumber...");

  const payload = JSON.stringify({
    jsonrpc: "2.0",
    method: "eth_blockNumber",
    params: [],
    id: 1,
  });

  const response = http.post(RPC_URL, payload, requestConfig);

  const isSuccess = check(response, {
    "eth_blockNumber 状态200": (r) => {
      if (r.status !== 200) {
        recordError(
          `eth_blockNumber HTTP错误 - 状态码: ${
            r.status
          }, 响应: ${r.body.substring(0, 200)}`
        );
        return false;
      }
      return true;
    },
    "eth_blockNumber 响应正常": (r) => {
      try {
        const data = JSON.parse(r.body);
        if (data.error) {
          recordError(
            `eth_blockNumber RPC错误 - ${JSON.stringify(data.error)}`
          );
          return false;
        }
        if (!data.result) {
          recordError(`eth_blockNumber 无结果 - 响应: ${r.body}`);
          return false;
        }
        return true;
      } catch (e) {
        recordError(
          `eth_blockNumber 解析错误 - ${e.message}, 响应: ${r.body.substring(
            0,
            200
          )}`
        );
        return false;
      }
    },
  });

  if (!isSuccess) {
    console.error(
      `❌ eth_blockNumber 失败: ${response.status} ${response.body}`
    );
    return null;
  }

  try {
    const data = JSON.parse(response.body);
    const blockNumber = data.result;
    const blockNumberDec = parseInt(blockNumber, 16);

    console.log(`   ✅ 最新区块号: ${blockNumber} (${blockNumberDec})`);
    return blockNumber;
  } catch (error) {
    console.error(`❌ 解析区块号失败: ${error.message}`);
    return null;
  }
}

/**
 * 通过区块号获取区块信息
 */
function getBlockByNumber(blockNumber) {
  console.log(`📡 2. 调用 eth_getBlockByNumber(${blockNumber})...`);

  const payload = JSON.stringify({
    jsonrpc: "2.0",
    method: "eth_getBlockByNumber",
    params: [blockNumber, true], // true = 返回完整交易信息
    id: 2,
  });

  const response = http.post(RPC_URL, payload, requestConfig);

  const isSuccess = check(response, {
    "eth_getBlockByNumber 状态200": (r) => {
      if (r.status !== 200) {
        recordError(
          `eth_getBlockByNumber HTTP错误 - 状态码: ${
            r.status
          }, 响应: ${r.body.substring(0, 200)}`
        );
        return false;
      }
      return true;
    },
    "eth_getBlockByNumber 响应正常": (r) => {
      try {
        const data = JSON.parse(r.body);
        if (data.error) {
          recordError(
            `eth_getBlockByNumber RPC错误 - ${JSON.stringify(data.error)}`
          );
          return false;
        }
        if (!data.result || !data.result.hash) {
          recordError(
            `eth_getBlockByNumber 无效结果 - 响应: ${r.body.substring(0, 300)}`
          );
          return false;
        }
        return true;
      } catch (e) {
        recordError(
          `eth_getBlockByNumber 解析错误 - ${
            e.message
          }, 响应: ${r.body.substring(0, 200)}`
        );
        return false;
      }
    },
  });

  if (!isSuccess) {
    console.error(
      `❌ eth_getBlockByNumber 失败: ${response.status} ${response.body}`
    );
    return null;
  }

  try {
    const data = JSON.parse(response.body);
    const block = data.result;

    if (!block) {
      console.error(`❌ 区块 ${blockNumber} 不存在`);
      return null;
    }

    console.log(`   ✅ 区块hash: ${block.hash}`);
    console.log(
      `   📊 区块信息: 交易数=${block.transactions.length}, gas=${block.gasUsed}`
    );

    // 返回区块信息和第一笔交易的hash
    const firstTransactionHash =
      block.transactions.length > 0 ? block.transactions[0].hash : null;
    if (firstTransactionHash) {
      console.log(
        `   📝 第一笔交易hash: ${firstTransactionHash.substring(0, 10)}...`
      );
    } else {
      console.log(`   ⚠️  区块中没有交易`);
    }

    return {
      hash: block.hash,
      firstTransactionHash: firstTransactionHash,
    };
  } catch (error) {
    console.error(`❌ 解析区块信息失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取交易收据
 */
function getTransactionReceipt(transactionHash) {
  if (!transactionHash) {
    console.log(`⚠️  跳过获取交易收据：区块中没有交易`);
    return null;
  }

  console.log(
    `📡 3. 调用 eth_getTransactionReceipt(${transactionHash.substring(
      0,
      10
    )}...)...`
  );

  const payload = JSON.stringify({
    jsonrpc: "2.0",
    method: "eth_getTransactionReceipt",
    params: [transactionHash],
    id: 3,
  });

  const response = http.post(RPC_URL, payload, requestConfig);

  const isSuccess = check(response, {
    "eth_getTransactionReceipt 状态200": (r) => {
      if (r.status !== 200) {
        recordError(
          `eth_getTransactionReceipt HTTP错误 - 状态码: ${
            r.status
          }, 响应: ${r.body.substring(0, 200)}`
        );
        return false;
      }
      return true;
    },
    "eth_getTransactionReceipt 响应正常": (r) => {
      try {
        const data = JSON.parse(r.body);
        if (data.error) {
          recordError(
            `eth_getTransactionReceipt RPC错误 - ${JSON.stringify(data.error)}`
          );
          return false;
        }
        if (!data.result || !data.result.transactionHash) {
          recordError(
            `eth_getTransactionReceipt 无效结果 - 响应: ${r.body.substring(
              0,
              300
            )}`
          );
          return false;
        }
        return true;
      } catch (e) {
        recordError(
          `eth_getTransactionReceipt 解析错误 - ${
            e.message
          }, 响应: ${r.body.substring(0, 200)}`
        );
        return false;
      }
    },
  });

  if (!isSuccess) {
    console.error(
      `❌ eth_getTransactionReceipt 失败: ${response.status} ${response.body}`
    );
    console.error(`   请求的交易hash: ${transactionHash}`);
    return null;
  }

  try {
    const data = JSON.parse(response.body);
    const receipt = data.result;

    if (!receipt) {
      console.error(`❌ 交易收据 ${transactionHash} 不存在`);
      return null;
    }

    // 断言：返回内容不为空
    check(null, {
      交易收据不为空: () => {
        const isValid = receipt !== null && receipt !== undefined;
        if (!isValid) {
          recordError(`交易收据为空 - 交易hash: ${transactionHash}`);
        }
        return isValid;
      },
    });

    // 断言：有交易hash
    check(null, {
      交易收据包含交易hash: () => {
        const isValid =
          receipt.transactionHash && receipt.transactionHash.length > 0;
        if (!isValid) {
          recordError(
            `交易收据缺少交易hash - 收据内容: ${JSON.stringify(
              receipt
            ).substring(0, 200)}`
          );
        }
        return isValid;
      },
    });

    console.log(
      `   ✅ 交易收据获取成功: ${receipt.transactionHash.substring(0, 10)}...`
    );
    console.log(
      `   📊 收据信息: 状态=${receipt.status}, gasUsed=${
        receipt.gasUsed
      }, blockNumber=${parseInt(receipt.blockNumber, 16)}`
    );

    return receipt;
  } catch (error) {
    console.error(`❌ 解析交易收据失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试开始前的设置
 */
export function setup() {
  console.log("🚀 开始 BlockPI Arbitrum RPC 问题复现测试");
  console.log(`📍 RPC端点: ${RPC_URL}`);
  console.log(`🔧 测试配置: ${options.vus} VU, 持续时间: ${options.duration}`);
  console.log("💾 错误将自动保存到文件");
  console.log("=" * 60);
}

/**
 * 测试结束后的总结
 */
export function teardown() {
  console.log("=" * 60);
  console.log("🏁 测试完成");

  if (errorMessages.length > 0) {
    console.log(`\n� 共发现 ${errorMessages.length} 个错误`);

    // 生成错误文件内容
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `errors_${timestamp}.log`;

    // 输出错误到文件格式（K6 会捕获这些输出）
    console.log(`\n💾 正在保存错误到文件: ${filename}`);
    console.log("=== ERROR_FILE_START ===");
    console.log(`# BlockPI Arbitrum RPC 错误报告`);
    console.log(`# 生成时间: ${new Date().toISOString()}`);
    console.log(`# 总错误数: ${errorMessages.length}`);
    console.log(`# 测试配置: ${options.vus} VU, 持续时间: ${options.duration}`);
    console.log(`#`);
    errorMessages.forEach((msg) => console.log(msg));
    console.log("=== ERROR_FILE_END ===");

    console.log(`\n� 错误统计:`);
    // 统计错误类型
    const errorTypes = {};
    errorMessages.forEach((msg) => {
      const match = msg.match(/: (\w+)/);
      if (match) {
        const type = match[1];
        errorTypes[type] = (errorTypes[type] || 0) + 1;
      }
    });

    Object.entries(errorTypes).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} 次`);
    });

    console.log(`\n💾 自动保存错误文件:`);
    console.log(`   文件名: ${filename}`);
    console.log(
      `   提取命令: k6 run arbitrum_issue_reproduce.js 2>&1 | sed -n '/=== ERROR_FILE_START ===/,/=== ERROR_FILE_END ===/p' | grep -v '=== ERROR_FILE' > ${filename}`
    );
  } else {
    console.log("✅ 没有发现错误");
  }

  console.log("\n📝 常见问题:");
  console.log("   - 区块不存在: 可能是网络延迟导致的区块同步问题");
  console.log("   - 响应超时: 可能是RPC服务负载过高");
  console.log("   - 交易收据不存在: 可能是交易尚未确认或打包");
  console.log("   - 数据不一致: 可能是缓存或同步问题");

  console.log("\n🚀 快速运行命令:");
  console.log("   ./run_arbitrum_tests.sh quick  # 使用启动脚本");
  console.log(
    "   或直接运行: k6 run arbitrum_issue_reproduce.js 2>&1 | tee test_output.log"
  );
}
