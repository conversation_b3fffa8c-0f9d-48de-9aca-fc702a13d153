#!/bin/bash

# Arbitrum RPC 高级测试启动脚本
# 使用方法: ./run_arbitrum_advanced_test.sh [MODE] [VUS] [RPS] [DURATION] [API_KEY]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_MODE="quick"
DEFAULT_VUS=1
DEFAULT_RPS=1
DEFAULT_DURATION="30s"
DEFAULT_API_KEY="your-api-key-here"
DEFAULT_RANDOM_RANGE=100

# 读取命令行参数
MODE=${1:-$DEFAULT_MODE}
VUS=${2:-$DEFAULT_VUS}
RPS=${3:-$DEFAULT_RPS}
DURATION=${4:-$DEFAULT_DURATION}
API_KEY=${5:-$DEFAULT_API_KEY}

# 设置环境变量
export API_KEY=$API_KEY
export RANDOM_RANGE=$DEFAULT_RANDOM_RANGE

# 打印配置信息
print_config() {
    echo -e "${BLUE}=== Arbitrum RPC 高级测试配置 ===${NC}"
    echo -e "${GREEN}测试模式: ${MODE}${NC}"
    echo -e "${GREEN}VUs: ${VUS}${NC}"
    echo -e "${GREEN}RPS: ${RPS}${NC}"
    echo -e "${GREEN}持续时间: ${DURATION}${NC}"
    echo -e "${GREEN}API Key: ${API_KEY:0:12}...${NC}"
    echo -e "${GREEN}随机数范围: 0-${DEFAULT_RANDOM_RANGE}${NC}"
    echo -e "${BLUE}===================================${NC}"
}

# 验证依赖
check_dependencies() {
    echo -e "${YELLOW}🔍 检查依赖...${NC}"
    
    if ! command -v k6 &> /dev/null; then
        echo -e "${RED}❌ k6 未安装，请先安装 k6${NC}"
        echo -e "${YELLOW}macOS: brew install k6${NC}"
        echo -e "${YELLOW}Ubuntu: sudo apt install k6${NC}"
        exit 1
    fi
    
    if [[ "$API_KEY" == "your-api-key-here" ]]; then
        echo -e "${RED}❌ 请提供有效的 API Key${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查通过${NC}"
}

# 执行测试
run_test() {
    local script_file="arbitrum_rpc_advanced_test.js"
    
    case $MODE in
        "quick")
            echo -e "${YELLOW}🚀 执行快速测试...${NC}"
            k6 run \
                --vus $VUS \
                --rps $RPS \
                --iterations 10 \
                $script_file
            ;;
        "duration")
            echo -e "${YELLOW}⏱️  执行持续测试...${NC}"
            k6 run \
                --vus $VUS \
                --rps $RPS \
                --duration $DURATION \
                $script_file
            ;;
        "load")
            echo -e "${YELLOW}📊 执行负载测试...${NC}"
            k6 run \
                --vus $VUS \
                --rps $RPS \
                --duration $DURATION \
                --out json=arbitrum_test_results.json \
                $script_file
            ;;
        "stress")
            echo -e "${RED}🔥 执行压力测试...${NC}"
            k6 run \
                --vus $((VUS * 2)) \
                --rps $((RPS * 2)) \
                --duration $DURATION \
                --out json=arbitrum_stress_results.json \
                $script_file
            ;;
        *)
            echo -e "${RED}❌ 未知模式: $MODE${NC}"
            echo -e "${YELLOW}可用模式: quick, duration, load, stress${NC}"
            exit 1
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Arbitrum RPC 测试脚本使用说明${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo -e "  $0 [MODE] [VUS] [RPS] [DURATION] [API_KEY]"
    echo ""
    echo -e "${YELLOW}参数说明:${NC}"
    echo -e "  MODE        测试模式 (quick, duration, load, stress) - 默认: quick"
    echo -e "  VUS         虚拟用户数量 - 默认: 1"
    echo -e "  RPS         每秒请求数 - 默认: 1"
    echo -e "  DURATION    测试持续时间 (如 30s, 1m, 5m) - 默认: 30s"
    echo -e "  API_KEY     BlockPi API Key - 必需"
    echo ""
    echo -e "${YELLOW}测试模式说明:${NC}"
    echo -e "  quick       快速测试，执行10次迭代"
    echo -e "  duration    持续测试，运行指定时间"
    echo -e "  load        负载测试，生成JSON结果文件"
    echo -e "  stress      压力测试，双倍负载"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "  $0 quick 1 1 \"\" your-api-key"
    echo -e "  $0 duration 5 2 1m your-api-key"
    echo -e "  $0 load 10 5 2m your-api-key"
    echo -e "  $0 stress 20 10 30s your-api-key"
}

# 主函数
main() {
    # 显示帮助
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    # 打印配置
    print_config
    
    # 检查依赖
    check_dependencies
    
    # 执行测试
    echo -e "${YELLOW}🚀 开始执行测试...${NC}"
    echo ""
    
    run_test
    
    echo ""
    echo -e "${GREEN}✅ 测试完成！${NC}"
    
    # 检查是否生成了结果文件
    if [[ -f "arbitrum_test_results.json" ]]; then
        echo -e "${BLUE}📄 测试结果已保存到 arbitrum_test_results.json${NC}"
    fi
    
    if [[ -f "arbitrum_stress_results.json" ]]; then
        echo -e "${BLUE}📄 压力测试结果已保存到 arbitrum_stress_results.json${NC}"
    fi
}

# 执行主函数
main "$@"