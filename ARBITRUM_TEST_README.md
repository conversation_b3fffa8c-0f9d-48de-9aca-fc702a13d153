# BlockPI Arbitrum RPC 问题复现测试

本目录包含专门用于测试和复现 BlockPI Arbitrum RPC 问题的 K6 脚本。

## 📁 文件说明

### 1. `arbitrum_blockpi_test.js` - 完整测试脚本

- **功能**: 全面的 Arbitrum RPC 测试，包含详细的错误处理和数据验证
- **测试流程**: `eth_blockNumber` → `eth_getBlockByNumber` → `eth_getBlockByHash`
- **特点**:
  - 支持多虚拟用户并发测试
  - 详细的性能指标监控
  - 数据一致性验证
  - 完整的错误日志记录

### 2. `arbitrum_issue_reproduce.js` - 问题复现脚本

- **功能**: 简化版测试脚本，专门用于快速复现问题
- **测试流程**: 同上，但输出更简洁
- **特点**:
  - 单用户测试，便于观察问题
  - 清晰的步骤日志输出
  - 快速定位问题点

### 3. `arbitrum_advanced_test.js` - 高级测试脚本

- **功能**: 多场景综合测试，包含压力测试和历史区块测试
- **测试场景**:
  - 基础功能测试
  - 压力测试（渐进式负载）
  - 历史区块测试
- **特点**:
  - 多场景并行执行
  - 详细的性能统计
  - 边界情况测试
  - 自动故障转移

## 🚀 使用方法

### 前置条件

确保已安装 K6:

```bash
# macOS
brew install k6

# Ubuntu/Debian
sudo apt update && sudo apt install k6

# 或使用官方安装脚本
curl https://github.com/grafana/k6/releases/download/v0.47.0/k6-v0.47.0-linux-amd64.tar.gz -L | tar xvz --strip-components 1
```

### 运行测试

#### 1. 快速问题复现（推荐开始使用）

```bash
# 运行简化版测试脚本
k6 run arbitrum_issue_reproduce.js
```

#### 2. 完整性能测试

```bash
# 运行完整测试脚本
k6 run arbitrum_blockpi_test.js

# 自定义测试参数
k6 run --vus 5 --duration 1m arbitrum_blockpi_test.js

# 输出详细结果到文件
k6 run --out json=test_results.json arbitrum_blockpi_test.js
```

#### 3. 高级多场景测试

```bash
# 运行高级测试脚本（包含多个测试场景）
k6 run arbitrum_advanced_test.js

# 运行特定场景
k6 run -e K6_SCENARIO=stress_test arbitrum_advanced_test.js
k6 run -e K6_SCENARIO=historical_blocks arbitrum_advanced_test.js

# 生成详细报告
k6 run --out json=advanced_results.json arbitrum_advanced_test.js
```

### 测试参数说明

#### `arbitrum_issue_reproduce.js` 配置

```javascript
export let options = {
  vus: 1, // 虚拟用户数
  iterations: 5, // 总迭代次数
  duration: "30s", // 最大运行时间
};
```

#### `arbitrum_blockpi_test.js` 配置

```javascript
export let options = {
  duration: "2m", // 测试持续时间
  vus: 10, // 并发虚拟用户数

  // 阈值设置
  thresholds: {
    http_req_duration: ["p(95)<5000"], // 95%请求<5秒
    http_req_failed: ["rate<0.1"], // 失败率<10%
  },
};
```

## 🔍 测试场景

### 测试流程

1. **获取最新区块号** (`eth_blockNumber`)

   - 调用 BlockPI Arbitrum RPC 获取当前最新区块号
   - 验证响应格式和数据有效性

2. **通过区块号获取区块信息** (`eth_getBlockByNumber`)

   - 使用步骤 1 获取的区块号查询完整区块信息
   - 提取区块 hash 用于下一步测试

3. **通过区块 hash 获取区块信息** (`eth_getBlockByHash`)
   - 使用步骤 2 获取的区块 hash 查询区块信息
   - 验证与步骤 2 获取的数据是否一致

### 验证点

- ✅ HTTP 响应状态码 (200)
- ✅ RPC 响应格式正确性
- ✅ 响应时间性能指标
- ✅ 数据完整性和一致性
- ✅ 错误处理和重试机制

## 📊 结果分析

### 正常输出示例

```
🔄 开始第 1 次测试...
📡 1. 调用 eth_blockNumber...
   ✅ 最新区块号: 0x15a2c8b (22,765,707)
📡 2. 调用 eth_getBlockByNumber(0x15a2c8b)...
   ✅ 区块hash: 0x1234567890abcdef...
   📊 区块信息: 交易数=156, gas=29,999,944
📡 3. 调用 eth_getBlockByHash(0x1234567890...)...
   ✅ 通过hash获取成功: 区块号=0x15a2c8b (22,765,707)
   📊 区块信息: 交易数=156, gas=29,999,944
✅ 第 1 次测试完成
```

### 常见问题及解决方案

#### 1. 区块不存在错误

```
❌ 区块 0x15a2c8b 不存在
```

**可能原因**: 网络延迟导致的区块同步问题
**解决方案**: 重试或使用稍早的区块号

#### 2. 响应超时

```
❌ eth_getBlockByNumber 失败: timeout
```

**可能原因**: RPC 服务负载过高或网络问题
**解决方案**: 增加超时时间或减少并发数

#### 3. 数据不一致

```
❌ 数据不一致: hash一致性
```

**可能原因**: 缓存问题或节点同步延迟
**解决方案**: 检查不同 RPC 端点的数据

## 🛠 自定义配置

### 修改 RPC 端点

```javascript
// 在脚本中修改这个变量
const RPC_URL = "https://your-custom-arbitrum-rpc.com";
```

### 调整测试参数

```javascript
export let options = {
  // 阶段性测试
  stages: [
    { duration: "30s", target: 5 }, // 逐步增加负载
    { duration: "1m", target: 10 }, // 保持峰值负载
    { duration: "30s", target: 0 }, // 逐步减少负载
  ],

  // 自定义阈值
  thresholds: {
    http_req_duration: ["p(95)<3000"], // 更严格的响应时间要求
    http_req_failed: ["rate<0.05"], // 更严格的失败率要求
    "checks{method:eth_blockNumber}": ["rate>0.95"], // 特定方法的成功率
  },
};
```

## 📈 监控和报告

### 生成详细报告

```bash
# JSON 格式输出
k6 run --out json=results.json arbitrum_blockpi_test.js

# InfluxDB 输出（需要配置 InfluxDB）
k6 run --out influxdb=http://localhost:8086/k6 arbitrum_blockpi_test.js

# 多种输出格式
k6 run --out json=results.json --out influxdb=http://localhost:8086/k6 arbitrum_blockpi_test.js
```

### 关键指标

- `http_req_duration`: HTTP 请求响应时间
- `http_req_failed`: HTTP 请求失败率
- `http_reqs`: 每秒 HTTP 请求数
- `vus`: 活跃虚拟用户数
- `iterations`: 完成的迭代次数

## 🐛 故障排除

1. **K6 未安装**: 参考上面的安装说明
2. **网络连接问题**: 检查网络连接和防火墙设置
3. **RPC 限流**: 降低并发数或增加请求间隔
4. **脚本语法错误**: 检查 JavaScript 语法

## 📞 支持

如果遇到问题或需要帮助，请：

1. 检查 K6 官方文档: https://k6.io/docs/
2. 查看 BlockPI 文档: https://docs.blockpi.io/
3. 提供完整的错误日志和测试配置
