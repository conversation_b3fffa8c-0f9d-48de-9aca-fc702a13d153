# K6 Arbitrum RPC 测试脚本

这是一个专业的 k6 性能测试脚本，用于测试 Arbitrum BlockPI RPC 端点的 `eth_getBlockByNumber` 方法。

## 功能特性

- ✅ 支持通过命令行参数指定 VUS（虚拟用户数）和 RPS（每秒请求数）
- ✅ 动态生成区块高度（基于常量 `0x16181abe` + 随机数 0-99）
- ✅ 详细的请求和响应日志记录
- ✅ 自动提取和显示 `X-Node-Id` 和 `X-Gateway-Id` 响应头
- ✅ 精确的迭代时长统计
- ✅ 完整的错误处理和验证
- ✅ 专业的性能指标和阈值设置

## 文件说明

- `k6_arbitrum_rpc_test.js` - 主要的 k6 测试脚本
- `run_k6_arbitrum_test.sh` - 便捷的运行脚本
- `K6_ARBITRUM_TEST_README.md` - 本说明文档

## 安装要求

### 1. 安装 k6

**macOS (使用 Homebrew):**
```bash
brew install k6
```

**其他平台:**
请访问 [k6 官方安装指南](https://k6.io/docs/getting-started/installation/)

### 2. 验证安装
```bash
k6 version
```

## 使用方法

### 方法一：使用便捷脚本（推荐）

```bash
# 查看帮助信息
./run_k6_arbitrum_test.sh --help

# 低频测试（默认配置）
./run_k6_arbitrum_test.sh --api-key your_actual_api_key

# 自定义配置
./run_k6_arbitrum_test.sh --vus 5 --rps 2 --duration 60s --api-key your_actual_api_key
```

### 方法二：直接使用 k6 命令

```bash
# 基本用法
k6 run --env API_KEY=your_api_key --env VUS=1 --env RPS=1 k6_arbitrum_rpc_test.js

# 自定义配置
k6 run \
    --env API_KEY=your_api_key \
    --env VUS=5 \
    --env RPS=2 \
    --env DURATION=60s \
    k6_arbitrum_rpc_test.js
```

## 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--vus` / `VUS` | 虚拟用户数 | 1 | `--vus 5` |
| `--rps` / `RPS` | 每秒请求数 | 1 | `--rps 2` |
| `--duration` / `DURATION` | 测试持续时间 | 30s | `--duration 2m` |
| `--api-key` / `API_KEY` | BlockPI API Key | your_api_key_here | `--api-key abc123` |

**时间单位支持:**
- `s` - 秒
- `m` - 分钟  
- `h` - 小时

## 输出示例

### 控制台日志
```
================================================================================
K6 Arbitrum RPC Test Starting
API Endpoint: https://arbitrum.blockpi.network/v1/rpc/your_api_key
VUs: 1
RPS: 1
Duration: 30s
Initial Block Height: 0x16181abe
================================================================================

[2024-01-20T10:30:15.123Z] Block Height: 0x16181b1c, X-Node-Id: node-001, X-Gateway-Id: gw-002, Duration: 245ms
Successfully retrieved block 0x16181b1c with 156 transactions
[2024-01-20T10:30:16.456Z] Block Height: 0x16181af8, X-Node-Id: node-003, X-Gateway-Id: gw-001, Duration: 189ms
Successfully retrieved block 0x16181af8 with 203 transactions
```

### k6 性能报告
```
     ✓ status is 200
     ✓ response time < 2000ms
     ✓ has valid JSON-RPC response
     ✓ has X-Node-Id header
     ✓ has X-Gateway-Id header

     checks.........................: 100.00% ✓ 150      ✗ 0   
     data_received..................: 2.1 MB  70 kB/s
     data_sent......................: 15 kB   500 B/s
     errors.........................: 0.00%   ✓ 0        ✗ 0   
     http_req_blocked...............: avg=1.2ms    min=0.5ms    med=1.1ms    max=3.2ms    p(90)=2.1ms    p(95)=2.8ms   
     http_req_connecting............: avg=0.8ms    min=0.3ms    med=0.7ms    max=2.1ms    p(90)=1.4ms    p(95)=1.8ms   
     http_req_duration..............: avg=234.5ms  min=156ms    med=221ms    max=456ms    p(90)=312ms    p(95)=378ms   
     http_req_failed................: 0.00%   ✓ 0        ✗ 30  
     http_req_receiving.............: avg=2.1ms    min=0.8ms    med=1.9ms    max=5.2ms    p(90)=3.4ms    p(95)=4.1ms   
     http_req_sending...............: avg=0.3ms    min=0.1ms    med=0.2ms    max=0.8ms    p(90)=0.5ms    p(95)=0.6ms   
     http_req_waiting...............: avg=232.1ms  min=154ms    med=218ms    max=452ms    p(90)=309ms    p(95)=375ms   
     http_reqs......................: 30      1/s
     iteration_duration.............: avg=1.23s    min=1.15s    med=1.22s    max=1.45s    p(90)=1.31s    p(95)=1.38s   
     iterations.....................: 30      1/s
     vus............................: 1       min=1      max=1 
     vus_max........................: 1       min=1      max=1 
```

## 测试场景建议

### 低频测试（推荐用于生产环境监控）
```bash
./run_k6_arbitrum_test.sh --vus 1 --rps 1 --duration 5m --api-key your_api_key
```

### 中等负载测试
```bash
./run_k6_arbitrum_test.sh --vus 5 --rps 5 --duration 2m --api-key your_api_key
```

### 压力测试（请谨慎使用）
```bash
./run_k6_arbitrum_test.sh --vus 10 --rps 10 --duration 1m --api-key your_api_key
```

## 性能指标说明

- **http_req_duration**: HTTP 请求总时长
- **http_req_waiting**: 等待响应的时间
- **http_req_blocked**: 请求被阻塞的时间
- **checks**: 验证检查通过率
- **errors**: 自定义错误率
- **iteration_duration**: 完整迭代时长

## 故障排除

### 常见问题

1. **k6 命令未找到**
   ```bash
   # 安装 k6
   brew install k6  # macOS
   ```

2. **API Key 无效**
   - 确保使用正确的 BlockPI API Key
   - 检查 API Key 是否有足够的配额

3. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

4. **权限问题**
   ```bash
   chmod +x run_k6_arbitrum_test.sh
   ```

### 调试模式

如需更详细的调试信息，可以添加 `--verbose` 参数：

```bash
k6 run --verbose --env API_KEY=your_api_key k6_arbitrum_rpc_test.js
```

## 注意事项

- 请合理设置 RPS，避免对服务器造成过大压力
- 建议先进行小规模测试，确认配置正确后再进行大规模测试
- 请确保有足够的 API 配额
- 测试过程中请监控服务器资源使用情况

## 技术实现细节

- 使用 `constant-arrival-rate` 执行器确保精确的 RPS 控制
- 自动生成随机区块高度（基于 `0x16181abe` + 0-99 随机数）
- 完整的 JSON-RPC 请求/响应验证
- 详细的性能指标收集和阈值设置
- 专业的错误处理和日志记录
