import http from 'k6/http';
import { Trend, Counter, Rate } from 'k6/metrics';

// 自定义指标
const rpcResponseTime = new Trend('rpc_response_time');
const requestCounter = new Counter('rpc_requests');
const successCounter = new Counter('rpc_success');
const errorCounter = new Counter('rpc_errors');
const successRate = new Rate('rpc_success_rate');

// 测试配置
const API_KEY = __ENV.API_KEY || 'your-api-key-here';
const BASE_BLOCK_HEIGHT = '0x16181abe';
const RANDOM_RANGE = parseInt(__ENV.RANDOM_RANGE || '100');

// 验证环境变量
function validateConfig() {
    if (!API_KEY || API_KEY === 'your-api-key-here') {
        throw new Error('请设置有效的 API_KEY 环境变量');
    }
    console.log(`配置验证通过 - API Key: ${API_KEY.substring(0, 8)}...`);
    console.log(`随机数范围: 0-${RANDOM_RANGE}`);
}

// 生成随机区块高度
function generateRandomBlockHeight() {
    const baseDecimal = parseInt(BASE_BLOCK_HEIGHT, 16);
    const randomOffset = Math.floor(Math.random() * (RANDOM_RANGE + 1));
    const newHeightDecimal = baseDecimal + randomOffset;
    const newHeightHex = '0x' + newHeightDecimal.toString(16);
    
    console.log(`区块高度计算: ${BASE_BLOCK_HEIGHT} (${baseDecimal}) + ${randomOffset} = ${newHeightHex} (${newHeightDecimal})`);
    
    return newHeightHex;
}

// 执行 JSON-RPC 请求
function executeEthGetBlockByNumber(blockHeight) {
    const url = `https://arbitrum.blockpi.network/v1/rpc/${API_KEY}`;
    
    const payload = {
        "method": "eth_getBlockByNumber",
        "params": [blockHeight, true],
        "id": Math.floor(Math.random() * 1000000),
        "jsonrpc": "2.0"
    };
    
    const params = {
        headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'k6-arbitrum-rpc-test/1.0',
            'X-Test-Type': 'performance'
        },
        tags: {
            test_type: 'eth_getBlockByNumber',
            block_height: blockHeight
        }
    };
    
    const startTime = Date.now();
    const response = http.post(url, JSON.stringify(payload), params);
    const endTime = Date.now();
    
    // 添加自定义时间戳
    response.customTimings = {
        ...response.timings,
        totalRequestTime: endTime - startTime
    };
    
    return response;
}

// 解析并验证响应
function validateResponse(response) {
    let isValid = false;
    let result = null;
    let error = null;
    
    try {
        const data = JSON.parse(response.body);
        if (data.error) {
            error = data.error;
        } else if (data.result && data.result.number) {
            result = data.result;
            isValid = true;
        } else {
            error = { message: 'Invalid response format' };
        }
    } catch (e) {
        error = { message: `JSON parse error: ${e.message}` };
    }
    
    return { isValid, result, error };
}

// 处理响应并记录详细信息
function processResponse(response, blockHeight, iterationStartTime) {
    const iterationEndTime = Date.now();
    const iterationDuration = iterationEndTime - iterationStartTime;
    
    // 记录请求计数
    requestCounter.add(1);
    
    // 提取响应头信息
    let nodeId = 'N/A';
    let gatewayId = 'N/A';
    let requestTraceId = 'N/A';
    
    if (response && response.headers) {
        nodeId = response.headers['X-Node-Id'] || 'N/A';
        gatewayId = response.headers['X-Gateway-Id'] || 'N/A';
        requestTraceId = response.headers['X-Request-Trace-Id'] || 'N/A';
    }
    
    // 验证响应
    const validation = validateResponse(response);
    successRate.add(validation.isValid);
    
    // 记录响应时间
    if (response && response.customTimings) {
        rpcResponseTime.add(response.customTimings.totalRequestTime);
    }
    
    // 详细日志输出
    console.log(`\n🔍 === 迭代 #${__ITER + 1} 详情 ===`);
    console.log(`📍 请求区块高度: ${blockHeight}`);
    console.log(`🏷️  X-Node-Id: ${nodeId}`);
    console.log(`🚪 X-Gateway-Id: ${gatewayId}`);
    console.log(`📊 X-Request-Trace-Id: ${requestTraceId}`);
    console.log(`⏱️  迭代耗时: ${iterationDuration}ms`);
    console.log(`🌐 HTTP 状态码: ${response.status}`);
    console.log(`⚡ 响应时间: ${response.customTimings?.totalRequestTime || 'N/A'}ms`);
    console.log(`📏 内容长度: ${response.body ? response.body.length : 0} bytes`);
    
    if (validation.isValid) {
        successCounter.add(1);
        console.log(`✅ 请求成功`);
        console.log(`🔗 区块哈希: ${validation.result.hash}`);
        console.log(`🔢 区块编号: ${validation.result.number}`);
        console.log(`📝 交易数量: ${validation.result.transactions ? validation.result.transactions.length : 0}`);
        console.log(`⏰ 时间戳: ${validation.result.timestamp}`);
        console.log(`👤 矿工地址: ${validation.result.miner}`);
    } else {
        errorCounter.add(1);
        console.log(`❌ 请求失败`);
        if (validation.error) {
            console.log(`🔴 错误信息: ${JSON.stringify(validation.error)}`);
        }
        if (response.body) {
            console.log(`📄 响应体预览: ${response.body.substring(0, 200)}...`);
        }
    }
    
    console.log(`📊 迭代统计 ===`);
    console.log(`✅ 成功请求: ${successCounter.values.count}`);
    console.log(`❌ 失败请求: ${errorCounter.values.count}`);
    console.log(`📈 成功率: ${((successCounter.values.count / requestCounter.values.count) * 100).toFixed(2)}%`);
    console.log(`======================\n`);
}

// 主要测试函数
export default function() {
    const iterationStartTime = Date.now();
    
    // 验证配置（仅在第一次迭代时）
    if (__ITER === 0) {
        validateConfig();
    }
    
    // 生成随机区块高度
    const blockHeight = generateRandomBlockHeight();
    
    // 执行 RPC 请求
    const response = executeEthGetBlockByNumber(blockHeight);
    
    // 处理响应
    processResponse(response, blockHeight, iterationStartTime);
}

// 测试结束时的汇总处理
export function handleSummary(data) {
    const totalRequests = data.metrics['rpc_requests'].values.count;
    const successRequests = data.metrics['rpc_success'].values.count;
    const errorRequests = data.metrics['rpc_errors'].values.count;
    const successRateValue = totalRequests > 0 ? (successRequests / totalRequests) * 100 : 0;
    
    const summary = {
        test_config: {
            total_iterations: totalRequests,
            api_key_prefix: API_KEY.substring(0, 8) + '...',
            random_range: RANDOM_RANGE,
            base_block_height: BASE_BLOCK_HEIGHT
        },
        performance_metrics: {
            total_requests: totalRequests,
            successful_requests: successRequests,
            failed_requests: errorRequests,
            success_rate: `${successRateValue.toFixed(2)}%`,
            avg_response_time: `${data.metrics['rpc_response_time'].values.avg.toFixed(2)}ms`,
            min_response_time: `${data.metrics['rpc_response_time'].values.min.toFixed(2)}ms`,
            max_response_time: `${data.metrics['rpc_response_time'].values.max.toFixed(2)}ms`,
            median_response_time: `${data.metrics['rpc_response_time'].values.med.toFixed(2)}ms`,
            p90_response_time: `${data.metrics['rpc_response_time'].values.p(90).toFixed(2)}ms`,
            p95_response_time: `${data.metrics['rpc_response_time'].values.p(95).toFixed(2)}ms`,
            p99_response_time: `${data.metrics['rpc_response_time'].values.p(99).toFixed(2)}ms`
        },
        timestamp: new Date().toISOString()
    };
    
    console.log('\n📊 === Arbitrum RPC 测试最终汇总 ===');
    console.log('🔧 测试配置:');
    console.log(`   总迭代次数: ${summary.test_config.total_iterations}`);
    console.log(`   API Key: ${summary.test_config.api_key_prefix}`);
    console.log(`   随机范围: 0-${summary.test_config.random_range}`);
    console.log(`   基础区块高度: ${summary.test_config.base_block_height}`);
    
    console.log('\n📈 性能指标:');
    console.log(`   总请求数: ${summary.performance_metrics.total_requests}`);
    console.log(`   成功请求数: ${summary.performance_metrics.successful_requests}`);
    console.log(`   失败请求数: ${summary.performance_metrics.failed_requests}`);
    console.log(`   成功率: ${summary.performance_metrics.success_rate}`);
    console.log(`   平均响应时间: ${summary.performance_metrics.avg_response_time}`);
    console.log(`   最小响应时间: ${summary.performance_metrics.min_response_time}`);
    console.log(`   最大响应时间: ${summary.performance_metrics.max_response_time}`);
    console.log(`   中位数响应时间: ${summary.performance_metrics.median_response_time}`);
    console.log(`   P90 响应时间: ${summary.performance_metrics.p90_response_time}`);
    console.log(`   P95 响应时间: ${summary.performance_metrics.p95_response_time}`);
    console.log(`   P99 响应时间: ${summary.performance_metrics.p99_response_time}`);
    console.log('=====================================\n');
    
    // 返回 JSON 格式的汇总报告
    return {
        'stdout': JSON.stringify(summary, null, 2) + '\n',
        'arbitrum_test_summary.json': JSON.stringify(summary, null, 2)
    };
}