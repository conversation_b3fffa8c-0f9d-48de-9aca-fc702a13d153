import http from 'k6/http';
import { Trend, Counter } from 'k6/metrics';

// 自定义指标
const rpcResponseTime = new Trend('rpc_response_time');
const requestCounter = new Counter('rpc_requests');
const successCounter = new Counter('rpc_success');
const errorCounter = new Counter('rpc_errors');

// 从环境变量获取 API Key
const API_KEY = __ENV.API_KEY || 'your-api-key-here';
const BASE_BLOCK_HEIGHT = '0x16181abe';

// 生成随机区块高度
function generateRandomBlockHeight() {
    const baseDecimal = parseInt(BASE_BLOCK_HEIGHT, 16);
    const randomOffset = Math.floor(Math.random() * 101); // 0-100
    const newHeightDecimal = baseDecimal + randomOffset;
    return '0x' + newHeightDecimal.toString(16);
}

// 执行 JSON-RPC 请求
function executeEthGetBlockByNumber(blockHeight) {
    const url = `https://arbitrum.blockpi.network/v1/rpc/${API_KEY}`;
    
    const payload = {
        "method": "eth_getBlockByNumber",
        "params": [blockHeight, true],
        "id": 1,
        "jsonrpc": "2.0"
    };
    
    const params = {
        headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'k6-json-rpc-test/1.0'
        }
    };
    
    return http.post(url, JSON.stringify(payload), params);
}

// 处理响应并提取信息
function processResponse(response, blockHeight, iterationStartTime) {
    const iterationEndTime = Date.now();
    const iterationDuration = iterationEndTime - iterationStartTime;
    
    // 记录请求计数
    requestCounter.add(1);
    
    // 记录响应时间
    if (response && response.timings) {
        rpcResponseTime.add(response.timings.duration);
    }
    
    // 提取响应头信息
    let nodeId = 'N/A';
    let gatewayId = 'N/A';
    
    if (response && response.headers) {
        nodeId = response.headers['X-Node-Id'] || 'N/A';
        gatewayId = response.headers['X-Gateway-Id'] || 'N/A';
    }
    
    // 检查响应状态
    if (response.status === 200) {
        successCounter.add(1);
        
        // 解析 JSON 响应
        let responseData = null;
        try {
            responseData = JSON.parse(response.body);
        } catch (e) {
            console.error(`JSON 解析错误: ${e.message}`);
            errorCounter.add(1);
        }
        
        // 打印详细信息
        console.log(`=== 迭代详情 ===`);
        console.log(`请求区块高度: ${blockHeight}`);
        console.log(`X-Node-Id: ${nodeId}`);
        console.log(`X-Gateway-Id: ${gatewayId}`);
        console.log(`迭代耗时: ${iterationDuration}ms`);
        console.log(`HTTP 状态码: ${response.status}`);
        console.log(`响应时间: ${response.timings.duration}ms`);
        
        if (responseData) {
            if (responseData.error) {
                console.log(`RPC 错误: ${JSON.stringify(responseData.error)}`);
                errorCounter.add(1);
            } else if (responseData.result) {
                console.log(`区块哈希: ${responseData.result.hash || 'N/A'}`);
                console.log(`交易数量: ${responseData.result.transactions ? responseData.result.transactions.length : 0}`);
            }
        }
        
        console.log(`=================`);
        
    } else {
        errorCounter.add(1);
        console.log(`=== 迭代详情 ===`);
        console.log(`请求区块高度: ${blockHeight}`);
        console.log(`X-Node-Id: ${nodeId}`);
        console.log(`X-Gateway-Id: ${gatewayId}`);
        console.log(`迭代耗时: ${iterationDuration}ms`);
        console.log(`HTTP 错误状态码: ${response.status}`);
        console.log(`响应体: ${response.body}`);
        console.log(`=================`);
    }
}

// 主要测试函数
export default function() {
    const iterationStartTime = Date.now();
    
    // 生成随机区块高度
    const blockHeight = generateRandomBlockHeight();
    
    // 执行 RPC 请求
    const response = executeEthGetBlockByNumber(blockHeight);
    
    // 处理响应
    processResponse(response, blockHeight, iterationStartTime);
}

// 测试结束时的汇总处理
export function handleSummary(data) {
    const summary = {
        '总请求数': data.metrics['rpc_requests'].values.count,
        '成功请求数': data.metrics['rpc_success'].values.count,
        '失败请求数': data.metrics['rpc_errors'].values.count,
        '平均响应时间': `${data.metrics['rpc_response_time'].values.avg}ms`,
        '最小响应时间': `${data.metrics['rpc_response_time'].values.min}ms`,
        '最大响应时间': `${data.metrics['rpc_response_time'].values.max}ms`,
        '中位数响应时间': `${data.metrics['rpc_response_time'].values.med}ms`,
        'P90 响应时间': `${data.metrics['rpc_response_time'].values.p(90)}ms`,
        'P95 响应时间': `${data.metrics['rpc_response_time'].values.p(95)}ms`,
    };
    
    console.log('\n=== 测试汇总 ===');
    for (const [key, value] of Object.entries(summary)) {
        console.log(`${key}: ${value}`);
    }
    console.log('===============');
    
    return {
        stdout: JSON.stringify(summary, null, 2) + '\n'
    };
}