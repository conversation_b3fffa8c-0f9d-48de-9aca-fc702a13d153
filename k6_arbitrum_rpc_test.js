import http from 'k6/http';
import { check } from 'k6';
import { Rate } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('errors');

// 配置选项 - 通过命令行参数覆盖
export let options = {
  // 默认配置，可通过命令行 --vus 和 --rps 覆盖
  vus: __ENV.VUS || 1,
  rps: __ENV.RPS || 1,
  duration: __ENV.DURATION || '30s',
  
  // 使用 constant-arrival-rate 执行器来控制 RPS
  scenarios: {
    constant_rps: {
      executor: 'constant-arrival-rate',
      rate: parseInt(__ENV.RPS || '1'), // 每秒请求数
      timeUnit: '1s',
      duration: __ENV.DURATION || '30s',
      preAllocatedVUs: parseInt(__ENV.VUS || '1'),
      maxVUs: parseInt(__ENV.VUS || '1') * 2, // 允许动态扩展
    },
  },
  
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% 的请求应在 2 秒内完成
    errors: ['rate<0.1'], // 错误率应低于 10%
  },
};

// 从环境变量获取 API Key，如果没有则使用默认值
const API_KEY = __ENV.API_KEY || 'your_api_key_here';
const BASE_URL = `https://arbitrum.blockpi.network/v1/rpc/${API_KEY}`;

// 初始区块高度常量（十六进制）
const INITIAL_BLOCK_HEIGHT = 0x16181abe;

/**
 * 生成随机区块高度
 * @returns {string} 十六进制格式的区块高度
 */
function generateRandomBlockHeight() {
  // 生成 0-99 的随机数
  const randomOffset = Math.floor(Math.random() * 100);
  // 加到初始区块高度上
  const blockHeight = INITIAL_BLOCK_HEIGHT + randomOffset;
  // 转换为十六进制字符串
  return '0x' + blockHeight.toString(16);
}

/**
 * 创建 eth_getBlockByNumber 请求体
 * @param {string} blockHeight 区块高度（十六进制）
 * @returns {object} JSON-RPC 请求体
 */
function createGetBlockByNumberRequest(blockHeight) {
  return {
    method: 'eth_getBlockByNumber',
    params: [blockHeight, true],
    id: 1,
    jsonrpc: '2.0'
  };
}

/**
 * 执行 JSON-RPC 请求
 * @param {object} requestBody 请求体
 * @returns {object} 响应对象
 */
function executeJsonRpcRequest(requestBody) {
  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: '30s',
  };

  return http.post(BASE_URL, JSON.stringify(requestBody), params);
}

/**
 * 打印请求和响应信息
 * @param {string} blockHeight 区块高度
 * @param {object} response HTTP 响应对象
 * @param {number} iterationDuration 迭代时长（毫秒）
 */
function logRequestInfo(blockHeight, response, iterationDuration) {
  const xNodeId = response.headers['X-Node-Id'] || 'N/A';
  const xGatewayId = response.headers['X-Gateway-Id'] || 'N/A';
  
  console.log(`[${new Date().toISOString()}] Block Height: ${blockHeight}, X-Node-Id: ${xNodeId}, X-Gateway-Id: ${xGatewayId}, Duration: ${iterationDuration}ms`);
}

/**
 * 验证响应
 * @param {object} response HTTP 响应对象
 * @returns {boolean} 验证是否通过
 */
function validateResponse(response) {
  const checks = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2000ms': (r) => r.timings.duration < 2000,
    'has valid JSON-RPC response': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.jsonrpc === '2.0' && (body.result !== undefined || body.error !== undefined);
      } catch (e) {
        return false;
      }
    },
    'has X-Node-Id header': (r) => r.headers['X-Node-Id'] !== undefined,
    'has X-Gateway-Id header': (r) => r.headers['X-Gateway-Id'] !== undefined,
  });

  if (!checks) {
    errorRate.add(1);
    console.error(`Request failed: Status ${response.status}, Body: ${response.body}`);
  }

  return checks;
}

/**
 * 主测试函数
 */
export default function () {
  const iterationStart = Date.now();
  
  // 生成随机区块高度
  const blockHeight = generateRandomBlockHeight();
  
  // 创建请求体
  const requestBody = createGetBlockByNumberRequest(blockHeight);
  
  // 执行请求
  const response = executeJsonRpcRequest(requestBody);
  
  // 计算迭代时长
  const iterationDuration = Date.now() - iterationStart;
  
  // 验证响应
  const isValid = validateResponse(response);
  
  // 打印请求信息
  logRequestInfo(blockHeight, response, iterationDuration);
  
  // 如果响应有效，可以进一步处理响应数据
  if (isValid && response.status === 200) {
    try {
      const responseBody = JSON.parse(response.body);
      if (responseBody.result) {
        // 可以在这里添加对区块数据的进一步验证
        console.log(`Successfully retrieved block ${blockHeight} with ${responseBody.result.transactions?.length || 0} transactions`);
      } else if (responseBody.error) {
        console.error(`JSON-RPC Error: ${JSON.stringify(responseBody.error)}`);
        errorRate.add(1);
      }
    } catch (e) {
      console.error(`Failed to parse response body: ${e.message}`);
      errorRate.add(1);
    }
  }
}

/**
 * 测试设置函数 - 在测试开始前执行
 */
export function setup() {
  console.log('='.repeat(80));
  console.log('K6 Arbitrum RPC Test Starting');
  console.log(`API Endpoint: ${BASE_URL}`);
  console.log(`VUs: ${options.scenarios.constant_rps.preAllocatedVUs}`);
  console.log(`RPS: ${options.scenarios.constant_rps.rate}`);
  console.log(`Duration: ${options.duration}`);
  console.log(`Initial Block Height: 0x${INITIAL_BLOCK_HEIGHT.toString(16)}`);
  console.log('='.repeat(80));
}

/**
 * 测试清理函数 - 在测试结束后执行
 */
export function teardown() {
  console.log('='.repeat(80));
  console.log('K6 Arbitrum RPC Test Completed');
  console.log('='.repeat(80));
}
