# Arbitrum RPC 测试使用说明

## 概述

本项目包含了两个 k6 脚本用于测试 Arbitrum JSON-RPC endpoint，专门针对 `eth_getBlockByNumber` 方法进行性能测试。

## 文件说明

### 1. `arbitrum_rpc_test.js` - 基础测试脚本
- 简单易用的基础版本
- 包含核心功能和基本日志输出
- 适合快速验证和基本测试

### 2. `arbitrum_rpc_advanced_test.js` - 高级测试脚本
- 功能完整的版本，包含详细监控
- 支持多种性能指标和详细分析
- 生成 JSON 格式的测试报告
- 包含错误处理和验证逻辑

### 3. `run_arbitrum_test.sh` - 基础启动脚本
- 简单的启动脚本
- 支持基本参数配置
- 适合快速测试

### 4. `run_arbitrum_advanced_test.sh` - 高级启动脚本
- 功能完整的启动脚本
- 支持多种测试模式（quick, duration, load, stress）
- 包含依赖检查和错误处理
- 支持彩色输出和详细日志

## 功能特性

### 核心功能
- ✅ 随机区块高度生成（基于 `0x16181abe` + 0-100 随机数）
- ✅ JSON-RPC 请求执行
- ✅ 响应头信息提取（`X-Node-Id`, `X-Gateway-Id`）
- ✅ 迭代时间测量和记录
- ✅ 详细日志输出
- ✅ 性能指标统计

### 高级功能（仅高级脚本）
- ✅ 多种测试模式支持
- ✅ JSON 结果文件生成
- ✅ 响应验证和错误处理
- ✅ 详细的性能分析报告
- ✅ 成功率统计
- ✅ 百分位数统计（P90, P95, P99）

## 使用方法

### 基础脚本使用

```bash
# 设置 API Key 环境变量
export API_KEY="your-actual-api-key"

# 使用基础启动脚本
./run_arbitrum_test.sh [VUs] [RPS] [ITERATIONS] [API_KEY]

# 示例
./run_arbitrum_test.sh 1 1 10 your-api-key
```

### 高级脚本使用

```bash
# 设置 API Key 环境变量
export API_KEY="your-actual-api-key"

# 使用高级启动脚本
./run_arbitrum_advanced_test.sh [MODE] [VUs] [RPS] [DURATION] [API_KEY]

# 示例
./run_arbitrum_advanced_test.sh quick 1 1 "" your-api-key
./run_arbitrum_advanced_test.sh duration 5 2 1m your-api-key
./run_arbitrum_advanced_test.sh load 10 5 2m your-api-key
./run_arbitrum_advanced_test.sh stress 20 10 30s your-api-key
```

### 直接使用 k6 命令

```bash
# 基础脚本
k6 run --vus 1 --rps 1 --iterations 10 arbitrum_rpc_test.js

# 高级脚本
k6 run --vus 1 --rps 1 --duration 30s arbitrum_rpc_advanced_test.js

# 生成 JSON 结果
k6 run --vus 5 --rps 3 --duration 1m --out json=results.json arbitrum_rpc_advanced_test.js
```

## 测试模式说明

### 1. Quick（快速测试）
- 执行 10 次迭代
- 适合快速验证功能
- 输出基本性能指标

### 2. Duration（持续测试）
- 运行指定时间
- 适合稳定性测试
- 输出详细统计信息

### 3. Load（负载测试）
- 生成 JSON 结果文件
- 适合性能分析
- 包含完整的性能报告

### 4. Stress（压力测试）
- 双倍负载运行
- 适合极限测试
- 测试系统稳定性

## 输出示例

### 控制台输出
```
🔍 === 迭代 #1 详情 ===
📍 请求区块高度: 0x16181bc5
🏷️  X-Node-Id: node-123
🚪 X-Gateway-Id: gateway-456
📊 X-Request-Trace-Id: trace-789
⏱️  迭代耗时: 245ms
🌐 HTTP 状态码: 200
⚡ 响应时间: 230ms
📏 内容长度: 2048 bytes
✅ 请求成功
🔗 区块哈希: 0x123...abc
🔢 区块编号: 0x16181bc5
📝 交易数量: 15
⏰ 时间戳: 1634567890
👤 矿工地址: 0x456...def
```

### 汇总报告
```json
{
  "test_config": {
    "total_iterations": 10,
    "api_key_prefix": "your-api-",
    "random_range": 100,
    "base_block_height": "0x16181abe"
  },
  "performance_metrics": {
    "total_requests": 10,
    "successful_requests": 9,
    "failed_requests": 1,
    "success_rate": "90.00%",
    "avg_response_time": "245.50ms",
    "min_response_time": "200.00ms",
    "max_response_time": "320.00ms",
    "median_response_time": "240.00ms",
    "p90_response_time": "300.00ms",
    "p95_response_time": "310.00ms",
    "p99_response_time": "320.00ms"
  }
}
```

## 配置说明

### 环境变量
- `API_KEY`: BlockPi API Key（必需）
- `RANDOM_RANGE`: 随机数范围，默认 100（可选）

### 脚本参数
- `--vus`: 虚拟用户数量
- `--rps`: 每秒请求数
- `--iterations`: 迭代次数
- `--duration`: 测试持续时间

### 可调整的配置
```javascript
// 基础区块高度
const BASE_BLOCK_HEIGHT = '0x16181abe';

// 随机数范围
const RANDOM_RANGE = 100;

// 测试参数
const VUS = 1;
const RPS = 1;
const DURATION = '30s';
```

## 注意事项

### 1. API Key 安全
- 请勿将 API Key 提交到版本控制系统
- 建议使用环境变量传递 API Key
- 在生产环境中使用适当的密钥管理方案

### 2. 网络要求
- 确保网络连接稳定
- 考虑网络延迟对测试结果的影响
- 建议在目标网络环境中进行测试

### 3. 资源消耗
- 高负载测试可能消耗大量网络带宽
- 监控系统资源使用情况
- 避免对生产环境造成过大压力

### 4. 结果分析
- 关注成功率指标
- 分析响应时间的分布
- 观察不同负载下的性能表现

## 故障排除

### 常见问题

1. **API Key 错误**
   ```
   Error: 请设置有效的 API_KEY 环境变量
   ```
   解决方案：检查 API Key 是否正确设置

2. **网络连接问题**
   ```
   Error: unable to connect to host
   ```
   解决方案：检查网络连接和防火墙设置

3. **k6 未安装**
   ```
   Error: k6: command not found
   ```
   解决方案：安装 k6 工具

4. **JSON 解析错误**
   ```
   Error: JSON parse error
   ```
   解决方案：检查 API 响应格式

### 调试技巧

1. **启用详细日志**
   ```bash
   k6 run --vus 1 --rps 1 --iterations 1 arbitrum_rpc_advanced_test.js
   ```

2. **检查网络连接**
   ```bash
   curl -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' https://arbitrum.blockpi.network/v1/rpc/your-api-key
   ```

3. **验证 API Key**
   确保提供的 API Key 具有访问 Arbitrum 网络的权限

## 扩展功能

### 添加新的 RPC 方法
```javascript
function executeEthCall() {
    const payload = {
        "method": "eth_call",
        "params": [toAddress, data, "latest"],
        "id": 1,
        "jsonrpc": "2.0"
    };
    return http.post(url, JSON.stringify(payload), params);
}
```

### 自定义指标
```javascript
const customMetric = new Trend('custom_metric_name');
customMetric.add(value);
```

### 多节点测试
可以扩展脚本以支持多个 RPC 端点，比较不同节点的性能表现。

---

## 技术支持

如有问题或建议，请参考 k6 官方文档或联系技术支持团队。