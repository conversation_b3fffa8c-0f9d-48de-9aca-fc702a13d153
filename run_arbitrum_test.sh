#!/bin/bash

# Arbitrum RPC 测试启动脚本
# 使用方法: ./run_arbitrum_test.sh [VUs] [RPS] [ITERATIONS] [API_KEY]

# 设置默认值
DEFAULT_VUS=1
DEFAULT_RPS=1
DEFAULT_ITERATIONS=10
DEFAULT_API_KEY="your-api-key-here"

# 读取命令行参数
VUS=${1:-$DEFAULT_VUS}
RPS=${2:-$DEFAULT_RPS}
ITERATIONS=${3:-$DEFAULT_ITERATIONS}
API_KEY=${4:-$DEFAULT_API_KEY}

# 设置环境变量
export API_KEY=$API_KEY

echo "=== Arbitrum RPC 测试配置 ==="
echo "VUs: $VUS"
echo "RPS: $RPS"
echo "迭代次数: $ITERATIONS"
echo "API Key: ${API_KEY:0:10}..."
echo "============================="

# 执行 k6 测试
k6 run \
  --vus $VUS \
  --rps $RPS \
  --iterations $ITERATIONS \
  arbitrum_rpc_test.js

echo "测试完成！"